import React, { createContext, useContext, useState, useEffect } from 'react';
import jwt from 'jsonwebtoken';
import Cookies from 'js-cookie';

// Create Auth Context
const AuthContext = createContext();

// Custom hook to use auth context
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

// Mock JWT secret (in real app, this would be on the server)
const JWT_SECRET = 'your-secret-key-here';

// Auth Provider Component
export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);

  // Check for existing token on mount
  useEffect(() => {
    const token = Cookies.get('authToken');
    if (token) {
      try {
        const decoded = jwt.verify(token, JWT_SECRET);
        setUser(decoded);
      } catch (error) {
        console.error('Invalid token:', error);
        Cookies.remove('authToken');
      }
    }
    setLoading(false);
  }, []);

  // Login function
  const login = async (email, password) => {
    try {
      // Mock authentication - in real app, this would be an API call
      const mockUsers = [
        { id: 1, email: '<EMAIL>', password: 'demo123', name: 'Demo User' },
        { id: 2, email: '<EMAIL>', password: 'test123', name: 'Test User' }
      ];

      const foundUser = mockUsers.find(
        u => u.email === email && u.password === password
      );

      if (!foundUser) {
        throw new Error('Invalid email or password');
      }

      // Create JWT token
      const token = jwt.sign(
        { 
          id: foundUser.id, 
          email: foundUser.email, 
          name: foundUser.name 
        },
        JWT_SECRET,
        { expiresIn: '24h' }
      );

      // Store token in cookie
      Cookies.set('authToken', token, { expires: 1 }); // 1 day

      // Set user state
      setUser({ id: foundUser.id, email: foundUser.email, name: foundUser.name });

      return { success: true };
    } catch (error) {
      return { success: false, error: error.message };
    }
  };

  // Signup function
  const signup = async (name, email, password) => {
    try {
      // Mock signup - in real app, this would be an API call
      // For demo purposes, we'll just create a token with the provided data
      const newUser = {
        id: Date.now(), // Simple ID generation
        name,
        email
      };

      // Create JWT token
      const token = jwt.sign(newUser, JWT_SECRET, { expiresIn: '24h' });

      // Store token in cookie
      Cookies.set('authToken', token, { expires: 1 });

      // Set user state
      setUser(newUser);

      return { success: true };
    } catch (error) {
      return { success: false, error: error.message };
    }
  };

  // Logout function
  const logout = () => {
    Cookies.remove('authToken');
    setUser(null);
  };

  // Check if user is authenticated
  const isAuthenticated = () => {
    return !!user;
  };

  const value = {
    user,
    login,
    signup,
    logout,
    isAuthenticated,
    loading
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export default AuthContext;
