import React, { createContext, useContext, useState, useEffect } from 'react';

// Create Auth Context
const AuthContext = createContext();

// Custom hook to use auth context
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

// Auth Provider Component
export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);

  // Check for existing user data on mount
  useEffect(() => {
    const userData = localStorage.getItem('quizUser');
    if (userData) {
      try {
        const parsedUser = JSON.parse(userData);
        setUser(parsedUser);
      } catch (error) {
        console.error('Invalid user data:', error);
        localStorage.removeItem('quizUser');
      }
    }
    setLoading(false);
  }, []);

  // Login function
  const login = async (email, password) => {
    try {
      // Mock authentication - in real app, this would be an API call
      const mockUsers = [
        { id: 1, email: '<EMAIL>', password: 'demo123', name: 'Demo User' },
        { id: 2, email: '<EMAIL>', password: 'test123', name: 'Test User' }
      ];

      const foundUser = mockUsers.find(
        u => u.email === email && u.password === password
      );

      if (!foundUser) {
        throw new Error('Invalid email or password');
      }

      // Create user object
      const userData = {
        id: foundUser.id,
        email: foundUser.email,
        name: foundUser.name,
        loginTime: new Date().toISOString()
      };

      // Store user data in localStorage
      localStorage.setItem('quizUser', JSON.stringify(userData));

      // Set user state
      setUser(userData);

      return { success: true };
    } catch (error) {
      return { success: false, error: error.message };
    }
  };

  // Signup function
  const signup = async (name, email, password) => {
    try {
      // Mock signup - in real app, this would be an API call
      const newUser = {
        id: Date.now(), // Simple ID generation
        name,
        email,
        signupTime: new Date().toISOString()
      };

      // Store user data in localStorage
      localStorage.setItem('quizUser', JSON.stringify(newUser));

      // Set user state
      setUser(newUser);

      return { success: true };
    } catch (error) {
      return { success: false, error: error.message };
    }
  };

  // Logout function
  const logout = () => {
    localStorage.removeItem('quizUser');
    setUser(null);
  };

  // Check if user is authenticated
  const isAuthenticated = () => {
    return !!user;
  };

  const value = {
    user,
    login,
    signup,
    logout,
    isAuthenticated,
    loading
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export default AuthContext;
