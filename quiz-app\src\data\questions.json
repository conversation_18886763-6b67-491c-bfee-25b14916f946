{"categories": {"javascript": {"name": "JavaScript", "description": "Test your JavaScript knowledge", "questions": [{"id": 1, "question": "What is the correct way to declare a variable in JavaScript?", "options": ["var myVariable;", "variable myVariable;", "v myVariable;", "declare myVariable;"], "correctAnswer": 0, "explanation": "In JavaScript, variables are declared using 'var', 'let', or 'const' keywords. 'var' is the traditional way to declare variables."}, {"id": 2, "question": "Which method is used to add an element to the end of an array?", "options": ["append()", "push()", "add()", "insert()"], "correctAnswer": 1, "explanation": "The push() method adds one or more elements to the end of an array and returns the new length of the array."}, {"id": 3, "question": "What does '===' operator do in JavaScript?", "options": ["Assigns a value", "Compares values only", "Compares values and types", "Declares a variable"], "correctAnswer": 2, "explanation": "The '===' operator performs strict equality comparison, checking both value and type without type coercion."}, {"id": 4, "question": "Which of the following is NOT a JavaScript data type?", "options": ["String", "Boolean", "Float", "Number"], "correctAnswer": 2, "explanation": "JavaScript has Number type for all numeric values. There's no separate 'Float' type - all numbers are stored as floating-point."}, {"id": 5, "question": "What is the output of: console.log(typeof null)?", "options": ["null", "undefined", "object", "boolean"], "correctAnswer": 2, "explanation": "This is a well-known JavaScript quirk. typeof null returns 'object', which is considered a bug in the language but is kept for backward compatibility."}]}, "react": {"name": "React", "description": "Test your React.js knowledge", "questions": [{"id": 6, "question": "What is JSX in React?", "options": ["A JavaScript library", "A syntax extension for JavaScript", "A CSS framework", "A database"], "correctAnswer": 1, "explanation": "JSX is a syntax extension for JavaScript that allows you to write HTML-like code in your JavaScript files."}, {"id": 7, "question": "Which hook is used to manage state in functional components?", "options": ["useEffect", "useState", "useContext", "useReducer"], "correctAnswer": 1, "explanation": "useState is the primary hook for managing local state in functional React components."}, {"id": 8, "question": "What is the purpose of useEffect hook?", "options": ["To manage state", "To handle side effects", "To create components", "To style components"], "correctAnswer": 1, "explanation": "useEffect is used to perform side effects in functional components, such as data fetching, subscriptions, or manually changing the DOM."}, {"id": 9, "question": "How do you pass data from parent to child component?", "options": ["Using state", "Using props", "Using context", "Using refs"], "correctAnswer": 1, "explanation": "Props (properties) are used to pass data from parent components to child components in React."}, {"id": 10, "question": "What is the virtual DOM?", "options": ["A real DOM element", "A JavaScript representation of the real DOM", "A CSS framework", "A database"], "correctAnswer": 1, "explanation": "The virtual DOM is a JavaScript representation of the real DOM kept in memory and synced with the real DOM through a process called reconciliation."}]}, "general": {"name": "General Programming", "description": "Test your general programming knowledge", "questions": [{"id": 11, "question": "What does API stand for?", "options": ["Application Programming Interface", "Advanced Programming Interface", "Application Process Interface", "Advanced Process Interface"], "correctAnswer": 0, "explanation": "API stands for Application Programming Interface, which is a set of protocols and tools for building software applications."}, {"id": 12, "question": "Which of the following is a version control system?", "options": ["Git", "HTML", "CSS", "JSON"], "correctAnswer": 0, "explanation": "Git is a distributed version control system used to track changes in source code during software development."}, {"id": 13, "question": "What does HTTP stand for?", "options": ["HyperText Transfer Protocol", "HyperText Transmission Protocol", "High Transfer Text Protocol", "HyperText Transport Protocol"], "correctAnswer": 0, "explanation": "HTTP stands for HyperText Transfer Protocol, which is the foundation of data communication on the World Wide Web."}, {"id": 14, "question": "Which HTTP status code indicates 'Not Found'?", "options": ["200", "404", "500", "301"], "correctAnswer": 1, "explanation": "HTTP status code 404 indicates that the requested resource could not be found on the server."}, {"id": 15, "question": "What is the purpose of a database index?", "options": ["To store data", "To improve query performance", "To backup data", "To encrypt data"], "correctAnswer": 1, "explanation": "Database indexes are used to improve the speed of data retrieval operations by creating shortcuts to data locations."}]}}}