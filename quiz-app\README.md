# 🧠 QuizMaster - Interactive Quiz Web Application

A modern, interactive quiz web application built with React.js featuring a beautiful neumorphic design, user authentication, and comprehensive dashboard analytics.

## ✨ Features

### 🔐 User Authentication
- **JWT-based authentication** with secure token management
- **Login & Signup** functionality with form validation
- **Demo account** for quick testing (`<EMAIL>` / `demo123`)
- **Protected routes** ensuring secure access

### 🎨 Neumorphic UI Design
- **Soft shadows and gradients** for a modern look
- **Light color palette** with excellent contrast
- **Smooth animations** using Framer Motion
- **Fully responsive** design for all devices

### 📝 Interactive Quiz System
- **Multiple choice questions** with 4 options each
- **Real-time feedback** with correct/incorrect indicators
- **Timer system** (30 seconds per question)
- **Progress tracking** with visual progress bar
- **Question-by-question navigation**

### 📊 Interactive Dashboard
- **Performance analytics** with score breakdown
- **Interactive charts** using Chart.js (pie & bar charts)
- **Quiz history** with local storage persistence
- **Answer review** with detailed explanations
- **Statistics tracking** (average score, best score, total time)

### 🎯 Quiz Categories
- **JavaScript** - Test your JS knowledge (5 questions)
- **React** - React.js concepts and hooks (5 questions)
- **General Programming** - Computer science fundamentals (5 questions)

## 🛠️ Technology Stack

- **Frontend**: React.js 18+ with Hooks
- **Routing**: React Router DOM
- **Animations**: Framer Motion
- **Charts**: Chart.js with React-ChartJS-2
- **Authentication**: JWT with js-cookie
- **Styling**: CSS3 with CSS Variables (Neumorphic Design)
- **Build Tool**: Vite
- **Package Manager**: npm

## 📦 Installation & Setup

### Prerequisites
- Node.js (v16 or higher)
- npm or yarn

### Quick Start

1. **Install dependencies**
   ```bash
   npm install
   ```

2. **Start the development server**
   ```bash
   npm run dev
   ```

3. **Open your browser**
   Navigate to `http://localhost:5173`

### Demo Credentials
For quick testing, use these demo credentials:
- **Email**: `<EMAIL>`
- **Password**: `demo123`

## 🎮 How to Use

### 1. Authentication
- Visit the application and sign up for a new account
- Or use the demo credentials for quick access
- Login to access the quiz dashboard

### 2. Taking a Quiz
- Select a quiz category from the home page
- Answer questions within the 30-second time limit
- Receive immediate feedback after each answer
- Track your progress with the visual progress bar

### 3. Dashboard Analytics
- View your quiz results with detailed statistics
- Analyze performance with interactive charts
- Review answers with explanations
- Check quiz history and track improvement

## 🏗️ Project Structure

```
quiz-app/
├── src/
│   ├── components/
│   │   ├── Auth/           # Authentication components
│   │   ├── Dashboard/      # Results and analytics
│   │   ├── Layout/         # Header and routing
│   │   └── Quiz/           # Quiz functionality
│   ├── context/            # React Context for state
│   ├── data/               # Quiz questions data
│   ├── App.jsx             # Main app component
│   └── index.css           # Global styles
├── package.json
└── README.md
```

## 🎨 Design Features

### Neumorphic Design System
- **Soft shadows** for depth and elegance
- **Light color palette** with excellent readability
- **Smooth transitions** and hover effects
- **Consistent spacing** and typography

### Responsive Design
- **Mobile-first** approach
- **Flexible layouts** that adapt to screen size
- **Touch-friendly** buttons and interactions
- **Optimized** for both desktop and mobile

## 🧪 Testing the Application

### Manual Testing Checklist
- [ ] User can sign up with valid information
- [ ] User can login with demo credentials
- [ ] Quiz timer counts down correctly
- [ ] Answers are recorded and scored properly
- [ ] Dashboard displays correct statistics
- [ ] Charts render with accurate data
- [ ] Quiz history persists between sessions

### Demo Flow
1. Open the application
2. Click "Use Demo Account" or enter demo credentials
3. Select a quiz category (JavaScript, React, or General)
4. Complete the quiz by answering questions
5. View results in the interactive dashboard
6. Review answers and check quiz history

## 🚀 Deployment

### Build for Production
```bash
npm run build
```

### Preview Production Build
```bash
npm run preview
```

## 🔧 Customization

### Adding New Questions
Edit `src/data/questions.json` to add new categories or questions:

```json
{
  "categories": {
    "newCategory": {
      "name": "New Category",
      "description": "Description here",
      "questions": [
        {
          "id": 1,
          "question": "Your question?",
          "options": ["A", "B", "C", "D"],
          "correctAnswer": 0,
          "explanation": "Explanation here"
        }
      ]
    }
  }
}
```

### Modifying Design
Update CSS variables in `src/index.css`:

```css
:root {
  --bg-primary: #e6e7ee;
  --accent-primary: #3498db;
  /* Add your custom colors */
}
```

## 📄 License

This project is open source and available under the MIT License.

---

**Built with ❤️ using React.js and modern web technologies**
