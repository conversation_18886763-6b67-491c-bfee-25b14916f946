/* Layout Components Styles */

/* Header Styles */
.header {
  background: var(--bg-primary);
  box-shadow: var(--shadow-outset);
  position: sticky;
  top: 0;
  z-index: 100;
  border-bottom: 1px solid rgba(127, 140, 141, 0.1);
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 0;
}

.logo {
  display: flex;
  align-items: center;
}

.logo-btn {
  background: none;
  border: none;
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  padding: 8px 16px;
  border-radius: 12px;
  transition: var(--transition-smooth);
  background: var(--bg-primary);
  box-shadow: var(--shadow-outset);
}

.logo-btn:hover {
  box-shadow: var(--shadow-outset-hover);
  transform: translateY(-1px);
}

.logo-btn:active {
  box-shadow: var(--shadow-pressed);
  transform: translateY(0);
}

.logo-icon {
  font-size: 28px;
  filter: drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.1));
}

.logo-text {
  font-size: 24px;
  font-weight: 700;
  color: var(--text-primary);
  background: linear-gradient(145deg, var(--accent-primary), var(--accent-secondary));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 20px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 16px;
  background: var(--bg-primary);
  border-radius: 12px;
  box-shadow: var(--shadow-inset-light), var(--shadow-inset-dark);
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(145deg, var(--accent-primary), var(--accent-secondary));
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 16px;
  box-shadow: var(--shadow-outset);
}

.user-details {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.user-name {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 14px;
}

.user-email {
  font-size: 12px;
  color: var(--text-secondary);
}

.logout-btn {
  background: linear-gradient(145deg, var(--error), #c0392b);
  color: white;
  font-weight: 600;
  padding: 10px 20px;
  font-size: 14px;
}

/* Loading Styles */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background: var(--bg-primary);
  gap: 20px;
}

.loading-spinner-large {
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.spinner-ring {
  width: 50px;
  height: 50px;
  border: 4px solid var(--bg-secondary);
  border-top: 4px solid var(--accent-primary);
  border-radius: 50%;
  box-shadow: var(--shadow-outset);
}

.loading-container p {
  color: var(--text-secondary);
  font-size: 16px;
  font-weight: 500;
}

/* Main Content Layout */
.main-content {
  min-height: calc(100vh - 80px);
  padding: 20px 0;
}

.page-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .header-content {
    padding: 12px 0;
  }
  
  .logo-btn {
    padding: 6px 12px;
  }
  
  .logo-icon {
    font-size: 24px;
  }
  
  .logo-text {
    font-size: 20px;
  }
  
  .user-info {
    padding: 6px 12px;
    gap: 8px;
  }
  
  .user-avatar {
    width: 32px;
    height: 32px;
    font-size: 14px;
  }
  
  .user-name {
    font-size: 13px;
  }
  
  .user-email {
    font-size: 11px;
  }
  
  .logout-btn {
    padding: 8px 16px;
    font-size: 13px;
  }
  
  .header-actions {
    gap: 12px;
  }
}

@media (max-width: 480px) {
  .user-details {
    display: none;
  }
  
  .header-actions {
    gap: 8px;
  }
  
  .logo-text {
    display: none;
  }
}
