import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useQuiz } from '../../context/QuizContext';
import Charts from './Charts';
import './Dashboard.css';

const Dashboard = () => {
  const {
    score,
    questions,
    userAnswers,
    timeSpent,
    currentCategory,
    resetQuiz,
    getCategories,
    startQuiz,
    quizHistory,
    getQuizStats
  } = useQuiz();

  const [showReview, setShowReview] = useState(false);
  const [showHistory, setShowHistory] = useState(false);

  const categories = getCategories();
  const stats = getQuizStats();
  const percentage = Math.round((score / questions.length) * 100);

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const getPerformanceMessage = () => {
    if (percentage >= 90) return { message: "Outstanding! 🏆", color: "var(--success)" };
    if (percentage >= 80) return { message: "Excellent! 🎉", color: "var(--success)" };
    if (percentage >= 70) return { message: "Good job! 👍", color: "var(--warning)" };
    if (percentage >= 60) return { message: "Not bad! 📚", color: "var(--warning)" };
    return { message: "Keep practicing! 💪", color: "var(--error)" };
  };

  const performance = getPerformanceMessage();

  const handleRetryQuiz = () => {
    startQuiz(currentCategory);
  };

  const handleNewQuiz = () => {
    resetQuiz();
  };

  return (
    <motion.div
      className="dashboard-container"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      {/* Quiz Results Header */}
      <motion.div
        className="results-header card"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
      >
        <div className="results-title">
          <h1>Quiz Complete!</h1>
          <p style={{ color: performance.color }}>{performance.message}</p>
        </div>
        
        <div className="score-display">
          <motion.div
            className="score-circle"
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.3, type: "spring", stiffness: 200 }}
          >
            <span className="score-percentage" style={{ color: performance.color }}>
              {percentage}%
            </span>
            <span className="score-fraction">
              {score}/{questions.length}
            </span>
          </motion.div>
        </div>
      </motion.div>

      {/* Performance Stats */}
      <motion.div
        className="stats-grid"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
      >
        <div className="stat-card card">
          <div className="stat-icon">⏱️</div>
          <div className="stat-content">
            <span className="stat-label">Time Taken</span>
            <span className="stat-value">{formatTime(timeSpent)}</span>
          </div>
        </div>

        <div className="stat-card card">
          <div className="stat-icon">✅</div>
          <div className="stat-content">
            <span className="stat-label">Correct Answers</span>
            <span className="stat-value">{score}</span>
          </div>
        </div>

        <div className="stat-card card">
          <div className="stat-icon">❌</div>
          <div className="stat-content">
            <span className="stat-label">Incorrect Answers</span>
            <span className="stat-value">{questions.length - score}</span>
          </div>
        </div>

        <div className="stat-card card">
          <div className="stat-icon">📊</div>
          <div className="stat-content">
            <span className="stat-label">Accuracy</span>
            <span className="stat-value">{percentage}%</span>
          </div>
        </div>
      </motion.div>

      {/* Charts */}
      <Charts 
        userAnswers={userAnswers} 
        score={score} 
        totalQuestions={questions.length} 
      />

      {/* Action Buttons */}
      <motion.div
        className="action-buttons"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.6 }}
      >
        <button
          className="btn btn-primary"
          onClick={handleRetryQuiz}
        >
          🔄 Retry Quiz
        </button>

        <button
          className="btn btn-success"
          onClick={() => setShowReview(!showReview)}
        >
          📝 Review Answers
        </button>

        <button
          className="btn"
          onClick={() => setShowHistory(!showHistory)}
        >
          📈 Quiz History
        </button>

        <button
          className="btn"
          onClick={handleNewQuiz}
        >
          🏠 New Quiz
        </button>
      </motion.div>

      {/* Answer Review Modal */}
      <AnimatePresence>
        {showReview && (
          <motion.div
            className="modal-overlay"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={() => setShowReview(false)}
          >
            <motion.div
              className="modal-content card"
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.8 }}
              onClick={(e) => e.stopPropagation()}
            >
              <div className="modal-header">
                <h2>Answer Review</h2>
                <button
                  className="close-btn"
                  onClick={() => setShowReview(false)}
                >
                  ✕
                </button>
              </div>
              
              <div className="review-content">
                {userAnswers.map((answer, index) => (
                  <div key={index} className="review-item">
                    <div className="review-question">
                      <span className="question-number">Q{index + 1}:</span>
                      <span>{answer.question}</span>
                    </div>
                    
                    <div className="review-answers">
                      <div className={`review-answer ${answer.isCorrect ? 'correct' : 'incorrect'}`}>
                        <strong>Your answer:</strong> {answer.options[answer.selectedAnswer] || 'No answer'}
                        {answer.isCorrect ? ' ✅' : ' ❌'}
                      </div>
                      
                      {!answer.isCorrect && (
                        <div className="review-answer correct">
                          <strong>Correct answer:</strong> {answer.options[answer.correctAnswer]} ✅
                        </div>
                      )}
                    </div>
                    
                    <div className="review-explanation">
                      <strong>Explanation:</strong> {answer.explanation}
                    </div>
                  </div>
                ))}
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Quiz History Modal */}
      <AnimatePresence>
        {showHistory && (
          <motion.div
            className="modal-overlay"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={() => setShowHistory(false)}
          >
            <motion.div
              className="modal-content card"
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.8 }}
              onClick={(e) => e.stopPropagation()}
            >
              <div className="modal-header">
                <h2>Quiz History</h2>
                <button
                  className="close-btn"
                  onClick={() => setShowHistory(false)}
                >
                  ✕
                </button>
              </div>
              
              <div className="history-stats">
                <div className="history-stat">
                  <span className="stat-label">Total Quizzes</span>
                  <span className="stat-value">{stats.totalQuizzes}</span>
                </div>
                <div className="history-stat">
                  <span className="stat-label">Average Score</span>
                  <span className="stat-value">{stats.averageScore}%</span>
                </div>
                <div className="history-stat">
                  <span className="stat-label">Best Score</span>
                  <span className="stat-value">{stats.bestScore}%</span>
                </div>
              </div>
              
              <div className="history-content">
                {quizHistory.length === 0 ? (
                  <p className="no-history">No quiz history available</p>
                ) : (
                  quizHistory.map((quiz, index) => (
                    <div key={quiz.id} className="history-item">
                      <div className="history-info">
                        <span className="history-category">{quiz.categoryName}</span>
                        <span className="history-date">
                          {new Date(quiz.date).toLocaleDateString()}
                        </span>
                      </div>
                      <div className="history-score">
                        <span className="score-text">{quiz.score}/{quiz.totalQuestions}</span>
                        <span className="percentage-text">{quiz.percentage}%</span>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
};

export default Dashboard;
