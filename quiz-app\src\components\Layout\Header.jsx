import React from 'react';
import { motion } from 'framer-motion';
import { useAuth } from '../../context/AuthContext';
import { useQuiz } from '../../context/QuizContext';
import './Layout.css';

const Header = () => {
  const { user, logout } = useAuth();
  const { resetQuiz, quizStarted } = useQuiz();

  const handleLogout = () => {
    resetQuiz(); // Reset quiz state when logging out
    logout();
  };

  const handleHomeClick = () => {
    if (quizStarted) {
      const confirmReset = window.confirm(
        'Are you sure you want to go home? Your current quiz progress will be lost.'
      );
      if (confirmReset) {
        resetQuiz();
      }
    } else {
      resetQuiz();
    }
  };

  return (
    <motion.header
      className="header"
      initial={{ y: -50, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      <div className="container">
        <div className="header-content">
          {/* Logo/Brand */}
          <motion.div
            className="logo"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <button onClick={handleHomeClick} className="logo-btn">
              <span className="logo-icon">🧠</span>
              <span className="logo-text">QuizMaster</span>
            </button>
          </motion.div>

          {/* User Info and Actions */}
          <div className="header-actions">
            {user && (
              <motion.div
                className="user-info"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.2 }}
              >
                <div className="user-avatar">
                  {user.name.charAt(0).toUpperCase()}
                </div>
                <div className="user-details">
                  <span className="user-name">{user.name}</span>
                  <span className="user-email">{user.email}</span>
                </div>
              </motion.div>
            )}

            <motion.button
              className="btn logout-btn"
              onClick={handleLogout}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              Logout
            </motion.button>
          </div>
        </div>
      </div>
    </motion.header>
  );
};

export default Header;
