/* Quiz Components Styles */

/* Quiz Container */
.quiz-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.quiz-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  gap: 20px;
}

.quiz-header {
  margin-bottom: 32px;
}

.quiz-stats {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
  gap: 16px;
}

.stat-item {
  background: var(--bg-primary);
  padding: 12px 20px;
  border-radius: 12px;
  box-shadow: var(--shadow-inset-light), var(--shadow-inset-dark);
  text-align: center;
  flex: 1;
}

.stat-label {
  display: block;
  font-size: 12px;
  color: var(--text-secondary);
  font-weight: 500;
  margin-bottom: 4px;
}

.stat-value {
  display: block;
  font-size: 18px;
  color: var(--text-primary);
  font-weight: 700;
}

/* Progress Bar */
.progress-container {
  background: var(--bg-primary);
  padding: 20px;
  border-radius: 16px;
  box-shadow: var(--shadow-outset);
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.progress-text {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 16px;
}

.progress-percentage {
  font-weight: 700;
  color: var(--accent-primary);
  font-size: 18px;
}

.progress-bar {
  position: relative;
  height: 12px;
  background: var(--bg-primary);
  border-radius: 6px;
  box-shadow: var(--shadow-inset-light), var(--shadow-inset-dark);
  overflow: hidden;
  margin-bottom: 16px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--accent-primary), var(--accent-secondary));
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(52, 152, 219, 0.3);
}

.progress-dots {
  display: flex;
  justify-content: space-between;
  gap: 8px;
}

.progress-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  transition: var(--transition-smooth);
}

.progress-dot.completed {
  background: var(--success);
  box-shadow: 0 2px 4px rgba(39, 174, 96, 0.3);
}

.progress-dot.current {
  background: var(--accent-primary);
  box-shadow: 0 2px 8px rgba(52, 152, 219, 0.5);
  transform: scale(1.2);
}

.progress-dot.upcoming {
  background: var(--bg-secondary);
  box-shadow: var(--shadow-inset-light), var(--shadow-inset-dark);
}

/* Question Container */
.question-container {
  position: relative;
}

.question-timer {
  position: absolute;
  top: -10px;
  right: 20px;
  z-index: 10;
}

.timer-circle {
  position: relative;
  width: 80px;
  height: 80px;
}

.timer-svg {
  width: 100%;
  height: 100%;
  transform: rotate(-90deg);
}

.timer-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 18px;
  font-weight: 700;
}

/* Question Card */
.question-card {
  padding: 32px;
  margin-top: 20px;
}

.question-header {
  margin-bottom: 20px;
}

.question-number {
  background: linear-gradient(145deg, var(--accent-primary), var(--accent-secondary));
  color: white;
  padding: 6px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 600;
  box-shadow: var(--shadow-outset);
}

.question-text {
  font-size: 24px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 32px;
  line-height: 1.4;
}

/* Options */
.options-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 24px;
}

.quiz-option {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px 20px;
  background: var(--bg-primary);
  border: none;
  border-radius: 12px;
  box-shadow: var(--shadow-outset);
  cursor: pointer;
  transition: var(--transition-smooth);
  text-align: left;
  width: 100%;
  position: relative;
}

.quiz-option:hover:not(.disabled) {
  box-shadow: var(--shadow-outset-hover);
  transform: translateY(-2px);
}

.quiz-option.selected {
  box-shadow: var(--shadow-pressed);
  background: linear-gradient(145deg, #e3f2fd, #bbdefb);
  border: 2px solid var(--accent-primary);
}

.quiz-option.correct {
  background: linear-gradient(145deg, #e8f5e8, #c8e6c9);
  border: 2px solid var(--success);
  box-shadow: 0 4px 12px rgba(39, 174, 96, 0.2);
}

.quiz-option.incorrect {
  background: linear-gradient(145deg, #ffebee, #ffcdd2);
  border: 2px solid var(--error);
  box-shadow: 0 4px 12px rgba(231, 76, 60, 0.2);
}

.quiz-option.disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

.option-letter {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: var(--bg-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  color: var(--text-primary);
  box-shadow: var(--shadow-inset-light), var(--shadow-inset-dark);
  flex-shrink: 0;
}

.quiz-option.selected .option-letter {
  background: var(--accent-primary);
  color: white;
  box-shadow: var(--shadow-outset);
}

.quiz-option.correct .option-letter {
  background: var(--success);
  color: white;
}

.quiz-option.incorrect .option-letter {
  background: var(--error);
  color: white;
}

.option-text {
  flex: 1;
  font-size: 16px;
  font-weight: 500;
  color: var(--text-primary);
  line-height: 1.4;
}

.option-icon {
  font-size: 20px;
  font-weight: 700;
  position: absolute;
  right: 20px;
}

.correct-icon {
  color: var(--success);
}

.incorrect-icon {
  color: var(--error);
}

/* Submit Button */
.submit-answer-btn {
  width: 100%;
  padding: 16px;
  font-size: 18px;
  font-weight: 600;
  margin-top: 8px;
}

/* Answer Feedback */
.answer-feedback {
  margin-top: 24px;
  padding: 20px;
  border-radius: 12px;
  background: var(--bg-primary);
  box-shadow: var(--shadow-inset-light), var(--shadow-inset-dark);
}

.feedback-message {
  font-size: 18px;
  font-weight: 700;
  margin-bottom: 12px;
  text-align: center;
}

.feedback-message.correct {
  color: var(--success);
}

.feedback-message.incorrect {
  color: var(--error);
}

.explanation {
  font-size: 14px;
  color: var(--text-secondary);
  line-height: 1.5;
  text-align: center;
}

.explanation strong {
  color: var(--text-primary);
}

/* Responsive Design */
@media (max-width: 768px) {
  .quiz-container {
    padding: 16px;
  }

  .question-card {
    padding: 24px 20px;
  }

  .question-text {
    font-size: 20px;
    margin-bottom: 24px;
  }

  .quiz-option {
    padding: 14px 16px;
    gap: 12px;
  }

  .option-letter {
    width: 28px;
    height: 28px;
    font-size: 14px;
  }

  .option-text {
    font-size: 14px;
  }

  .timer-circle {
    width: 60px;
    height: 60px;
  }

  .timer-text {
    font-size: 14px;
  }

  .quiz-stats {
    flex-direction: column;
    gap: 12px;
  }

  .stat-item {
    padding: 10px 16px;
  }

  .stat-value {
    font-size: 16px;
  }
}
