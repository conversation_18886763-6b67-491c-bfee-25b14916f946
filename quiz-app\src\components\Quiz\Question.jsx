import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import './Quiz.css';

const Question = ({ question, onAnswer, questionNumber }) => {
  const [selectedAnswer, setSelectedAnswer] = useState(null);
  const [showFeedback, setShowFeedback] = useState(false);
  const [timeLeft, setTimeLeft] = useState(30); // 30 seconds per question

  // Reset state when question changes
  useEffect(() => {
    setSelectedAnswer(null);
    setShowFeedback(false);
    setTimeLeft(30);
  }, [question.id]);

  // Timer countdown
  useEffect(() => {
    if (timeLeft > 0 && !showFeedback) {
      const timer = setTimeout(() => setTimeLeft(timeLeft - 1), 1000);
      return () => clearTimeout(timer);
    } else if (timeLeft === 0 && !showFeedback) {
      // Auto-submit when time runs out
      handleAnswerSubmit(null);
    }
  }, [timeLeft, showFeedback]);

  const handleAnswerSelect = (answerIndex) => {
    if (showFeedback) return; // Prevent selection after feedback
    setSelectedAnswer(answerIndex);
  };

  const handleAnswerSubmit = (answerIndex = selectedAnswer) => {
    setShowFeedback(true);
    
    // Show feedback for 2 seconds before moving to next question
    setTimeout(() => {
      onAnswer(answerIndex);
    }, 2000);
  };

  const getOptionClassName = (optionIndex) => {
    let className = 'quiz-option';
    
    if (showFeedback) {
      if (optionIndex === question.correctAnswer) {
        className += ' correct';
      } else if (optionIndex === selectedAnswer && optionIndex !== question.correctAnswer) {
        className += ' incorrect';
      } else {
        className += ' disabled';
      }
    } else if (selectedAnswer === optionIndex) {
      className += ' selected';
    }
    
    return className;
  };

  const getTimerColor = () => {
    if (timeLeft > 20) return 'var(--success)';
    if (timeLeft > 10) return 'var(--warning)';
    return 'var(--error)';
  };

  return (
    <AnimatePresence mode="wait">
      <motion.div
        key={question.id}
        className="question-container"
        initial={{ opacity: 0, x: 50 }}
        animate={{ opacity: 1, x: 0 }}
        exit={{ opacity: 0, x: -50 }}
        transition={{ duration: 0.5 }}
      >
        {/* Timer */}
        <div className="question-timer">
          <div className="timer-circle">
            <svg className="timer-svg" viewBox="0 0 100 100">
              <circle
                cx="50"
                cy="50"
                r="45"
                fill="none"
                stroke="var(--bg-secondary)"
                strokeWidth="8"
              />
              <motion.circle
                cx="50"
                cy="50"
                r="45"
                fill="none"
                stroke={getTimerColor()}
                strokeWidth="8"
                strokeLinecap="round"
                strokeDasharray={283}
                strokeDashoffset={283 - (283 * timeLeft) / 30}
                transform="rotate(-90 50 50)"
                transition={{ duration: 1, ease: "linear" }}
              />
            </svg>
            <span className="timer-text" style={{ color: getTimerColor() }}>
              {timeLeft}
            </span>
          </div>
        </div>

        {/* Question */}
        <div className="question-card card">
          <div className="question-header">
            <span className="question-number">Question {questionNumber}</span>
          </div>
          
          <h2 className="question-text">{question.question}</h2>
          
          <div className="options-container">
            {question.options.map((option, index) => (
              <motion.button
                key={index}
                className={getOptionClassName(index)}
                onClick={() => handleAnswerSelect(index)}
                disabled={showFeedback}
                whileHover={!showFeedback ? { scale: 1.02 } : {}}
                whileTap={!showFeedback ? { scale: 0.98 } : {}}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <span className="option-letter">
                  {String.fromCharCode(65 + index)}
                </span>
                <span className="option-text">{option}</span>
                
                {showFeedback && index === question.correctAnswer && (
                  <motion.span
                    className="option-icon correct-icon"
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{ delay: 0.2 }}
                  >
                    ✓
                  </motion.span>
                )}
                
                {showFeedback && index === selectedAnswer && index !== question.correctAnswer && (
                  <motion.span
                    className="option-icon incorrect-icon"
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{ delay: 0.2 }}
                  >
                    ✗
                  </motion.span>
                )}
              </motion.button>
            ))}
          </div>

          {/* Submit Button */}
          {selectedAnswer !== null && !showFeedback && (
            <motion.button
              className="btn btn-primary submit-answer-btn"
              onClick={() => handleAnswerSubmit()}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              Submit Answer
            </motion.button>
          )}

          {/* Feedback */}
          <AnimatePresence>
            {showFeedback && (
              <motion.div
                className="answer-feedback"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
              >
                <div className={`feedback-message ${
                  selectedAnswer === question.correctAnswer ? 'correct' : 'incorrect'
                }`}>
                  {selectedAnswer === question.correctAnswer ? (
                    <span>🎉 Correct!</span>
                  ) : (
                    <span>❌ Incorrect</span>
                  )}
                </div>
                
                <div className="explanation">
                  <strong>Explanation:</strong> {question.explanation}
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </motion.div>
    </AnimatePresence>
  );
};

export default Question;
