import React, { createContext, useContext, useState, useEffect } from 'react';
import questionsData from '../data/questions.json';

// Create Quiz Context
const QuizContext = createContext();

// Custom hook to use quiz context
export const useQuiz = () => {
  const context = useContext(QuizContext);
  if (!context) {
    throw new Error('useQuiz must be used within a QuizProvider');
  }
  return context;
};

// Quiz Provider Component
export const QuizProvider = ({ children }) => {
  const [currentCategory, setCurrentCategory] = useState(null);
  const [questions, setQuestions] = useState([]);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [userAnswers, setUserAnswers] = useState([]);
  const [quizStarted, setQuizStarted] = useState(false);
  const [quizCompleted, setQuizCompleted] = useState(false);
  const [score, setScore] = useState(0);
  const [timeSpent, setTimeSpent] = useState(0);
  const [quizHistory, setQuizHistory] = useState([]);

  // Load quiz history from localStorage on mount
  useEffect(() => {
    const savedHistory = localStorage.getItem('quizHistory');
    if (savedHistory) {
      setQuizHistory(JSON.parse(savedHistory));
    }
  }, []);

  // Save quiz history to localStorage whenever it changes
  useEffect(() => {
    localStorage.setItem('quizHistory', JSON.stringify(quizHistory));
  }, [quizHistory]);

  // Start a new quiz
  const startQuiz = (categoryKey) => {
    const category = questionsData.categories[categoryKey];
    if (!category) {
      throw new Error('Category not found');
    }

    setCurrentCategory(categoryKey);
    setQuestions(category.questions);
    setCurrentQuestionIndex(0);
    setUserAnswers([]);
    setQuizStarted(true);
    setQuizCompleted(false);
    setScore(0);
    setTimeSpent(0);
  };

  // Submit an answer
  const submitAnswer = (answerIndex) => {
    const currentQuestion = questions[currentQuestionIndex];
    const isCorrect = answerIndex === currentQuestion.correctAnswer;
    
    const answerData = {
      questionId: currentQuestion.id,
      selectedAnswer: answerIndex,
      correctAnswer: currentQuestion.correctAnswer,
      isCorrect,
      question: currentQuestion.question,
      options: currentQuestion.options,
      explanation: currentQuestion.explanation
    };

    setUserAnswers(prev => [...prev, answerData]);

    if (isCorrect) {
      setScore(prev => prev + 1);
    }

    // Move to next question or complete quiz
    if (currentQuestionIndex < questions.length - 1) {
      setCurrentQuestionIndex(prev => prev + 1);
    } else {
      completeQuiz();
    }
  };

  // Complete the quiz
  const completeQuiz = () => {
    setQuizCompleted(true);
    
    // Calculate final score
    const finalScore = userAnswers.filter(answer => answer.isCorrect).length + 
                      (userAnswers[userAnswers.length - 1]?.isCorrect ? 1 : 0);
    
    // Save to history
    const quizResult = {
      id: Date.now(),
      category: currentCategory,
      categoryName: questionsData.categories[currentCategory].name,
      score: finalScore,
      totalQuestions: questions.length,
      percentage: Math.round((finalScore / questions.length) * 100),
      timeSpent,
      date: new Date().toISOString(),
      answers: userAnswers
    };

    setQuizHistory(prev => [quizResult, ...prev.slice(0, 9)]); // Keep last 10 results
  };

  // Reset quiz
  const resetQuiz = () => {
    setCurrentCategory(null);
    setQuestions([]);
    setCurrentQuestionIndex(0);
    setUserAnswers([]);
    setQuizStarted(false);
    setQuizCompleted(false);
    setScore(0);
    setTimeSpent(0);
  };

  // Get current question
  const getCurrentQuestion = () => {
    return questions[currentQuestionIndex] || null;
  };

  // Get quiz progress
  const getProgress = () => {
    if (questions.length === 0) return 0;
    return Math.round(((currentQuestionIndex + 1) / questions.length) * 100);
  };

  // Get available categories
  const getCategories = () => {
    return Object.keys(questionsData.categories).map(key => ({
      key,
      ...questionsData.categories[key]
    }));
  };

  // Get quiz statistics
  const getQuizStats = () => {
    if (quizHistory.length === 0) {
      return {
        totalQuizzes: 0,
        averageScore: 0,
        bestScore: 0,
        totalTimeSpent: 0
      };
    }

    const totalQuizzes = quizHistory.length;
    const averageScore = Math.round(
      quizHistory.reduce((sum, quiz) => sum + quiz.percentage, 0) / totalQuizzes
    );
    const bestScore = Math.max(...quizHistory.map(quiz => quiz.percentage));
    const totalTimeSpent = quizHistory.reduce((sum, quiz) => sum + quiz.timeSpent, 0);

    return {
      totalQuizzes,
      averageScore,
      bestScore,
      totalTimeSpent
    };
  };

  const value = {
    // State
    currentCategory,
    questions,
    currentQuestionIndex,
    userAnswers,
    quizStarted,
    quizCompleted,
    score,
    timeSpent,
    quizHistory,
    
    // Actions
    startQuiz,
    submitAnswer,
    resetQuiz,
    setTimeSpent,
    
    // Getters
    getCurrentQuestion,
    getProgress,
    getCategories,
    getQuizStats
  };

  return (
    <QuizContext.Provider value={value}>
      {children}
    </QuizContext.Provider>
  );
};

export default QuizContext;
