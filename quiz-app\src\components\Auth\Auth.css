/* Authentication Components Styles */

.auth-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  background: var(--bg-primary);
}

.auth-card {
  width: 100%;
  max-width: 450px;
  padding: 40px;
  background: var(--bg-primary);
  border-radius: 24px;
  box-shadow: var(--shadow-outset);
}

.auth-header {
  text-align: center;
  margin-bottom: 32px;
}

.auth-header h2 {
  font-size: 28px;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 8px;
}

.auth-header p {
  color: var(--text-secondary);
  font-size: 16px;
}

.auth-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group label {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 14px;
}

.auth-submit-btn {
  margin-top: 8px;
  padding: 14px 24px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 12px;
  transition: var(--transition-smooth);
}

.auth-submit-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: var(--shadow-pressed);
}

.loading-spinner {
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.loading-spinner::after {
  content: '';
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.error-message {
  background: linear-gradient(145deg, #ffeaea, #ffcccc);
  color: var(--error);
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  border-left: 4px solid var(--error);
  box-shadow: var(--shadow-inset-light), var(--shadow-inset-dark);
}

.auth-divider {
  display: flex;
  align-items: center;
  margin: 24px 0;
  color: var(--text-secondary);
  font-size: 14px;
}

.auth-divider::before,
.auth-divider::after {
  content: '';
  flex: 1;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--text-secondary), transparent);
}

.auth-divider span {
  padding: 0 16px;
  background: var(--bg-primary);
}

.demo-btn {
  background: linear-gradient(145deg, var(--warning), #e67e22);
  color: white;
  font-weight: 600;
  margin-bottom: 16px;
}

.auth-footer {
  text-align: center;
  margin-top: 24px;
  padding-top: 24px;
  border-top: 1px solid rgba(127, 140, 141, 0.1);
}

.auth-footer p {
  color: var(--text-secondary);
  font-size: 14px;
}

.auth-link {
  background: none;
  border: none;
  color: var(--accent-primary);
  font-weight: 600;
  cursor: pointer;
  text-decoration: underline;
  font-size: inherit;
  padding: 0;
  transition: var(--transition-fast);
}

.auth-link:hover {
  color: var(--accent-secondary);
  transform: none;
  box-shadow: none;
}

.demo-credentials {
  background: linear-gradient(145deg, #f8f9fa, #e9ecef);
  padding: 16px;
  border-radius: 12px;
  margin-top: 20px;
  box-shadow: var(--shadow-inset-light), var(--shadow-inset-dark);
}

.demo-credentials h4 {
  color: var(--text-primary);
  font-size: 14px;
  margin-bottom: 8px;
  font-weight: 600;
}

.demo-credentials p {
  color: var(--text-secondary);
  font-size: 12px;
  margin: 4px 0;
  font-family: 'Courier New', monospace;
}

/* Responsive Design */
@media (max-width: 480px) {
  .auth-card {
    padding: 24px;
    border-radius: 20px;
  }
  
  .auth-header h2 {
    font-size: 24px;
  }
  
  .auth-header p {
    font-size: 14px;
  }
  
  .form-group label {
    font-size: 13px;
  }
  
  .input {
    padding: 10px 14px;
    font-size: 14px;
  }
  
  .auth-submit-btn {
    padding: 12px 20px;
    font-size: 14px;
  }
}
