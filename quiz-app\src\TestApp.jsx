function TestApp() {
  console.log('TestApp is rendering!');

  return (
    <div style={{
      padding: '20px',
      fontFamily: 'Arial, sans-serif',
      background: '#e6e7ee',
      minHeight: '100vh',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center'
    }}>
      <div style={{
        background: '#e6e7ee',
        padding: '40px',
        borderRadius: '20px',
        boxShadow: '9px 9px 16px #d1d9e6, -9px -9px 16px #ffffff',
        textAlign: 'center',
        maxWidth: '500px'
      }}>
        <h1 style={{ color: '#2c3e50', marginBottom: '20px' }}>
          🧠 QuizMaster Test
        </h1>
        <p style={{ color: '#7f8c8d', marginBottom: '30px' }}>
          If you can see this, React is working correctly!
        </p>
        <button
          onClick={() => alert('Button clicked!')}
          style={{
            background: 'linear-gradient(145deg, #3498db, #2980b9)',
            color: 'white',
            border: 'none',
            padding: '12px 24px',
            borderRadius: '12px',
            fontSize: '16px',
            fontWeight: '600',
            cursor: 'pointer',
            boxShadow: '9px 9px 16px #d1d9e6, -9px -9px 16px #ffffff'
          }}
        >
          Test Button
        </button>
      </div>
    </div>
  );
}

export default TestApp;
