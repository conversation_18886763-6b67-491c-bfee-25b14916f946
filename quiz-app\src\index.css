/* Neumorphic Quiz App - Global Styles */
:root {
  /* Neumorphic Color Palette */
  --bg-primary: #e6e7ee;
  --bg-secondary: #f0f1f8;
  --text-primary: #2c3e50;
  --text-secondary: #7f8c8d;
  --accent-primary: #3498db;
  --accent-secondary: #2980b9;
  --success: #27ae60;
  --error: #e74c3c;
  --warning: #f39c12;

  /* Neumorphic Shadows */
  --shadow-light: #ffffff;
  --shadow-dark: #d1d9e6;
  --shadow-inset-light: inset 2px 2px 5px #d1d9e6;
  --shadow-inset-dark: inset -2px -2px 5px #ffffff;
  --shadow-outset: 9px 9px 16px #d1d9e6, -9px -9px 16px #ffffff;
  --shadow-outset-hover: 6px 6px 12px #d1d9e6, -6px -6px 12px #ffffff;
  --shadow-pressed: inset 6px 6px 12px #d1d9e6, inset -6px -6px 12px #ffffff;

  /* Typography */
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
  font-weight: 400;

  /* Transitions */
  --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-fast: all 0.15s ease-in-out;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  background: var(--bg-primary);
  color: var(--text-primary);
  font-family: var(--font-family);
  min-height: 100vh;
  overflow-x: hidden;
}

/* Neumorphic Button Styles */
.btn {
  background: var(--bg-primary);
  border: none;
  border-radius: 12px;
  padding: 12px 24px;
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  cursor: pointer;
  transition: var(--transition-smooth);
  box-shadow: var(--shadow-outset);
  position: relative;
  overflow: hidden;
}

.btn:hover {
  box-shadow: var(--shadow-outset-hover);
  transform: translateY(-1px);
}

.btn:active {
  box-shadow: var(--shadow-pressed);
  transform: translateY(0);
}

.btn-primary {
  background: linear-gradient(145deg, var(--accent-primary), var(--accent-secondary));
  color: white;
}

.btn-success {
  background: linear-gradient(145deg, var(--success), #229954);
  color: white;
}

.btn-error {
  background: linear-gradient(145deg, var(--error), #c0392b);
  color: white;
}

/* Neumorphic Card Styles */
.card {
  background: var(--bg-primary);
  border-radius: 20px;
  padding: 24px;
  box-shadow: var(--shadow-outset);
  transition: var(--transition-smooth);
}

.card:hover {
  box-shadow: var(--shadow-outset-hover);
}

/* Neumorphic Input Styles */
.input {
  background: var(--bg-primary);
  border: none;
  border-radius: 12px;
  padding: 12px 16px;
  font-size: 16px;
  color: var(--text-primary);
  box-shadow: var(--shadow-inset-light), var(--shadow-inset-dark);
  transition: var(--transition-fast);
  width: 100%;
}

.input:focus {
  outline: none;
  box-shadow: var(--shadow-inset-light), var(--shadow-inset-dark),
              0 0 0 3px rgba(52, 152, 219, 0.1);
}

/* Utility Classes */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.text-center {
  text-align: center;
}

.mb-4 {
  margin-bottom: 1rem;
}

.mb-6 {
  margin-bottom: 1.5rem;
}

.mt-4 {
  margin-top: 1rem;
}

.flex {
  display: flex;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.grid {
  display: grid;
}

.hidden {
  display: none;
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: 0 16px;
  }

  .card {
    padding: 16px;
    border-radius: 16px;
  }

  .btn {
    padding: 10px 20px;
    font-size: 14px;
  }
}
