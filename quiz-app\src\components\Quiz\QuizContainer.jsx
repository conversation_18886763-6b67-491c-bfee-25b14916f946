import React, { useEffect } from 'react';
import { motion } from 'framer-motion';
import { useQuiz } from '../../context/QuizContext';
import Question from './Question';
import ProgressBar from './ProgressBar';
import './Quiz.css';

const QuizContainer = () => {
  const {
    getCurrentQuestion,
    getProgress,
    currentQuestionIndex,
    questions,
    submitAnswer,
    timeSpent,
    setTimeSpent
  } = useQuiz();

  const currentQuestion = getCurrentQuestion();
  const progress = getProgress();

  // Track time spent on quiz
  useEffect(() => {
    const timer = setInterval(() => {
      setTimeSpent(prev => prev + 1);
    }, 1000);

    return () => clearInterval(timer);
  }, [setTimeSpent]);

  const handleAnswer = (answerIndex) => {
    submitAnswer(answerIndex);
  };

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  if (!currentQuestion) {
    return (
      <div className="quiz-loading">
        <motion.div
          className="loading-spinner-large"
          animate={{ rotate: 360 }}
          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
        >
          <div className="spinner-ring"></div>
        </motion.div>
        <p>Loading quiz...</p>
      </div>
    );
  }

  return (
    <motion.div
      className="quiz-container"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      <div className="quiz-header">
        <ProgressBar
          progress={progress}
          currentQuestion={currentQuestionIndex + 1}
          totalQuestions={questions.length}
        />
        
        <div className="quiz-stats">
          <div className="stat-item">
            <span className="stat-label">Time Elapsed</span>
            <span className="stat-value">{formatTime(timeSpent)}</span>
          </div>
          <div className="stat-item">
            <span className="stat-label">Questions Left</span>
            <span className="stat-value">
              {questions.length - currentQuestionIndex - 1}
            </span>
          </div>
        </div>
      </div>

      <Question
        question={currentQuestion}
        onAnswer={handleAnswer}
        questionNumber={currentQuestionIndex + 1}
      />
    </motion.div>
  );
};

export default QuizContainer;
