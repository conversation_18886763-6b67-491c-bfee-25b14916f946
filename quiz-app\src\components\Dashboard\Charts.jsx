import React from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';
import { Bar, Pie } from 'react-chartjs-2';
import { motion } from 'framer-motion';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend
);

const Charts = ({ userAnswers, score, totalQuestions }) => {
  // Prepare data for answer distribution chart
  const correctAnswers = userAnswers.filter(answer => answer.isCorrect).length;
  const incorrectAnswers = totalQuestions - correctAnswers;

  const pieData = {
    labels: ['Correct', 'Incorrect'],
    datasets: [
      {
        data: [correctAnswers, incorrectAnswers],
        backgroundColor: [
          'rgba(39, 174, 96, 0.8)',
          'rgba(231, 76, 60, 0.8)',
        ],
        borderColor: [
          'rgba(39, 174, 96, 1)',
          'rgba(231, 76, 60, 1)',
        ],
        borderWidth: 2,
        hoverBackgroundColor: [
          'rgba(39, 174, 96, 0.9)',
          'rgba(231, 76, 60, 0.9)',
        ],
      },
    ],
  };

  // Prepare data for question-by-question performance
  const barData = {
    labels: userAnswers.map((_, index) => `Q${index + 1}`),
    datasets: [
      {
        label: 'Score',
        data: userAnswers.map(answer => answer.isCorrect ? 1 : 0),
        backgroundColor: userAnswers.map(answer => 
          answer.isCorrect 
            ? 'rgba(39, 174, 96, 0.8)' 
            : 'rgba(231, 76, 60, 0.8)'
        ),
        borderColor: userAnswers.map(answer => 
          answer.isCorrect 
            ? 'rgba(39, 174, 96, 1)' 
            : 'rgba(231, 76, 60, 1)'
        ),
        borderWidth: 2,
        borderRadius: 8,
        borderSkipped: false,
      },
    ],
  };

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top',
        labels: {
          usePointStyle: true,
          padding: 20,
          font: {
            size: 14,
            weight: '600',
          },
        },
      },
      tooltip: {
        backgroundColor: 'rgba(44, 62, 80, 0.9)',
        titleColor: '#ffffff',
        bodyColor: '#ffffff',
        borderColor: 'rgba(52, 152, 219, 0.8)',
        borderWidth: 1,
        cornerRadius: 8,
        padding: 12,
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        max: 1,
        ticks: {
          stepSize: 1,
          callback: function(value) {
            return value === 1 ? 'Correct' : value === 0 ? 'Wrong' : '';
          },
        },
        grid: {
          color: 'rgba(127, 140, 141, 0.1)',
        },
      },
      x: {
        grid: {
          color: 'rgba(127, 140, 141, 0.1)',
        },
      },
    },
  };

  const pieOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'bottom',
        labels: {
          usePointStyle: true,
          padding: 20,
          font: {
            size: 14,
            weight: '600',
          },
        },
      },
      tooltip: {
        backgroundColor: 'rgba(44, 62, 80, 0.9)',
        titleColor: '#ffffff',
        bodyColor: '#ffffff',
        borderColor: 'rgba(52, 152, 219, 0.8)',
        borderWidth: 1,
        cornerRadius: 8,
        padding: 12,
        callbacks: {
          label: function(context) {
            const percentage = ((context.parsed / totalQuestions) * 100).toFixed(1);
            return `${context.label}: ${context.parsed} (${percentage}%)`;
          },
        },
      },
    },
  };

  return (
    <div className="charts-container">
      <motion.div
        className="chart-card card"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
      >
        <h3 className="chart-title">Answer Distribution</h3>
        <div className="chart-wrapper pie-chart">
          <Pie data={pieData} options={pieOptions} />
        </div>
      </motion.div>

      <motion.div
        className="chart-card card"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
      >
        <h3 className="chart-title">Question-by-Question Performance</h3>
        <div className="chart-wrapper bar-chart">
          <Bar data={barData} options={chartOptions} />
        </div>
      </motion.div>
    </div>
  );
};

export default Charts;
