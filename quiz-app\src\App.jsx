import React, { useState, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate, useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';

// Context Providers
import { AuthProvider } from './context/AuthContext';
import { QuizProvider } from './context/QuizContext';

// Components
import { useAuth } from './context/AuthContext';
import { useQuiz } from './context/QuizContext';
import Header from './components/Layout/Header';
import ProtectedRoute from './components/Layout/ProtectedRoute';
import Login from './components/Auth/Login';
import Signup from './components/Auth/Signup';
import QuizContainer from './components/Quiz/QuizContainer';
import Dashboard from './components/Dashboard/Dashboard';

// Styles
import './App.css';

// Auth Page Component
const AuthPage = () => {
  const [isLogin, setIsLogin] = useState(true);

  return (
    <div className="auth-page">
      <AnimatePresence mode="wait">
        {isLogin ? (
          <Login
            key="login"
            onSwitchToSignup={() => setIsLogin(false)}
          />
        ) : (
          <Signup
            key="signup"
            onSwitchToLogin={() => setIsLogin(true)}
          />
        )}
      </AnimatePresence>
    </div>
  );
};

// Home Page Component
const HomePage = () => {
  const { getCategories, startQuiz } = useQuiz();
  const navigate = useNavigate();
  const categories = getCategories();

  const handleCategorySelect = (categoryKey) => {
    startQuiz(categoryKey);
    navigate('/quiz');
  };

  return (
    <motion.div
      className="home-page"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      <div className="container">
        <motion.div
          className="welcome-section"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <h1 className="welcome-title">Welcome to QuizMaster</h1>
          <p className="welcome-subtitle">
            Test your knowledge with our interactive quizzes
          </p>
        </motion.div>

        <motion.div
          className="categories-grid"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          {categories.map((category, index) => (
            <motion.div
              key={category.key}
              className="category-card card"
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.1 * index }}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <div className="category-icon">
                {category.key === 'javascript' && '🟨'}
                {category.key === 'react' && '⚛️'}
                {category.key === 'general' && '💻'}
              </div>
              <h3 className="category-name">{category.name}</h3>
              <p className="category-description">{category.description}</p>
              <div className="category-info">
                <span className="question-count">
                  {category.questions.length} Questions
                </span>
              </div>
              <button
                className="btn btn-primary category-btn"
                onClick={() => handleCategorySelect(category.key)}
              >
                Start Quiz
              </button>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </motion.div>
  );
};

// Quiz Page Component
const QuizPage = () => {
  const { quizStarted, quizCompleted } = useQuiz();

  if (quizCompleted) {
    return <Dashboard />;
  }

  if (quizStarted) {
    return <QuizContainer />;
  }

  return <Navigate to="/" replace />;
};

// Main App Component
const AppContent = () => {
  const { isAuthenticated } = useAuth();

  return (
    <div className="app">
      {isAuthenticated() && <Header />}

      <main className="main-content">
        <Routes>
          <Route
            path="/auth"
            element={
              isAuthenticated() ? <Navigate to="/" replace /> : <AuthPage />
            }
          />
          <Route
            path="/"
            element={
              <ProtectedRoute>
                <HomePage />
              </ProtectedRoute>
            }
          />
          <Route
            path="/quiz"
            element={
              <ProtectedRoute>
                <QuizPage />
              </ProtectedRoute>
            }
          />
          <Route path="*" element={<Navigate to="/" replace />} />
        </Routes>
      </main>
    </div>
  );
};

function App() {
  return (
    <Router>
      <AuthProvider>
        <QuizProvider>
          <AppContent />
        </QuizProvider>
      </AuthProvider>
    </Router>
  );
}

export default App;
