import React from 'react';
import { motion } from 'framer-motion';
import './Quiz.css';

const ProgressBar = ({ progress, currentQuestion, totalQuestions }) => {
  return (
    <div className="progress-container">
      <div className="progress-info">
        <span className="progress-text">
          Question {currentQuestion} of {totalQuestions}
        </span>
        <span className="progress-percentage">
          {Math.round(progress)}%
        </span>
      </div>
      
      <div className="progress-bar">
        <motion.div
          className="progress-fill"
          initial={{ width: 0 }}
          animate={{ width: `${progress}%` }}
          transition={{ 
            duration: 0.5, 
            ease: "easeOut" 
          }}
        />
        
        {/* Progress dots for each question */}
        <div className="progress-dots">
          {Array.from({ length: totalQuestions }, (_, index) => (
            <motion.div
              key={index}
              className={`progress-dot ${
                index < currentQuestion ? 'completed' : 
                index === currentQuestion - 1 ? 'current' : 'upcoming'
              }`}
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ 
                delay: index * 0.1,
                duration: 0.3 
              }}
              whileHover={{ scale: 1.2 }}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

export default ProgressBar;
