import type { TypedChartComponent } from './types.js';
export declare const Line: TypedChartComponent<"line">;
export declare const Bar: TypedChartComponent<"bar">;
export declare const Radar: TypedChartComponent<"radar">;
export declare const Doughnut: TypedChartComponent<"doughnut">;
export declare const PolarArea: TypedChartComponent<"polarArea">;
export declare const Bubble: TypedChartComponent<"bubble">;
export declare const Pie: TypedChartComponent<"pie">;
export declare const Scatter: TypedChartComponent<"scatter">;
//# sourceMappingURL=typedCharts.d.ts.map