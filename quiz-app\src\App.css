/* App Component Styles */

.app {
  min-height: 100vh;
  background: var(--bg-primary);
}

.main-content {
  min-height: calc(100vh - 80px);
}

/* Auth Page */
.auth-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--bg-primary);
}

/* Home Page */
.home-page {
  padding: 40px 0;
}

.welcome-section {
  text-align: center;
  margin-bottom: 48px;
}

.welcome-title {
  font-size: 48px;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 16px;
  background: linear-gradient(145deg, var(--accent-primary), var(--accent-secondary));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.welcome-subtitle {
  font-size: 20px;
  color: var(--text-secondary);
  font-weight: 500;
}

/* Categories Grid */
.categories-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 24px;
  max-width: 1000px;
  margin: 0 auto;
}

.category-card {
  padding: 32px 24px;
  text-align: center;
  transition: var(--transition-smooth);
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.category-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--accent-primary), var(--accent-secondary));
  transform: scaleX(0);
  transition: var(--transition-smooth);
}

.category-card:hover::before {
  transform: scaleX(1);
}

.category-icon {
  font-size: 48px;
  margin-bottom: 16px;
  filter: drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.1));
}

.category-name {
  font-size: 24px;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 12px;
}

.category-description {
  font-size: 16px;
  color: var(--text-secondary);
  margin-bottom: 20px;
  line-height: 1.5;
}

.category-info {
  margin-bottom: 24px;
}

.question-count {
  background: var(--bg-secondary);
  color: var(--text-secondary);
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 600;
  box-shadow: var(--shadow-inset-light), var(--shadow-inset-dark);
}

.category-btn {
  width: 100%;
  padding: 14px 24px;
  font-size: 16px;
  font-weight: 600;
}

/* Responsive Design */
@media (max-width: 768px) {
  .home-page {
    padding: 24px 0;
  }

  .welcome-title {
    font-size: 36px;
  }

  .welcome-subtitle {
    font-size: 18px;
  }

  .categories-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .category-card {
    padding: 24px 20px;
  }

  .category-icon {
    font-size: 40px;
  }

  .category-name {
    font-size: 20px;
  }

  .category-description {
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .welcome-title {
    font-size: 28px;
  }

  .welcome-subtitle {
    font-size: 16px;
  }

  .category-card {
    padding: 20px 16px;
  }
}
