/* Dashboard Components Styles */

.dashboard-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* Results Header */
.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32px;
  text-align: center;
}

.results-title h1 {
  font-size: 32px;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 8px;
}

.results-title p {
  font-size: 18px;
  font-weight: 600;
  margin: 0;
}

.score-display {
  display: flex;
  align-items: center;
  justify-content: center;
}

.score-circle {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background: var(--bg-primary);
  box-shadow: var(--shadow-outset);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
}

.score-percentage {
  font-size: 28px;
  font-weight: 700;
  line-height: 1;
}

.score-fraction {
  font-size: 14px;
  color: var(--text-secondary);
  font-weight: 500;
}

/* Stats Grid */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.stat-card {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
}

.stat-icon {
  font-size: 32px;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: var(--bg-primary);
  box-shadow: var(--shadow-inset-light), var(--shadow-inset-dark);
  display: flex;
  align-items: center;
  justify-content: center;
}

.stat-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.stat-label {
  font-size: 14px;
  color: var(--text-secondary);
  font-weight: 500;
}

.stat-value {
  font-size: 24px;
  color: var(--text-primary);
  font-weight: 700;
}

/* Charts */
.charts-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 24px;
}

.chart-card {
  padding: 24px;
}

.chart-title {
  font-size: 20px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 20px;
  text-align: center;
}

.chart-wrapper {
  position: relative;
  height: 300px;
}

.pie-chart {
  height: 250px;
}

.bar-chart {
  height: 300px;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  justify-content: center;
  gap: 16px;
  flex-wrap: wrap;
}

.action-buttons .btn {
  padding: 12px 24px;
  font-size: 16px;
  font-weight: 600;
  min-width: 140px;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.modal-content {
  max-width: 800px;
  max-height: 80vh;
  width: 100%;
  background: var(--bg-primary);
  border-radius: 20px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px;
  border-bottom: 1px solid rgba(127, 140, 141, 0.1);
}

.modal-header h2 {
  font-size: 24px;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  transition: var(--transition-fast);
}

.close-btn:hover {
  background: var(--bg-secondary);
  color: var(--text-primary);
  transform: none;
  box-shadow: none;
}

/* Review Content */
.review-content {
  padding: 24px;
  overflow-y: auto;
  flex: 1;
}

.review-item {
  margin-bottom: 24px;
  padding: 20px;
  background: var(--bg-secondary);
  border-radius: 12px;
  box-shadow: var(--shadow-inset-light), var(--shadow-inset-dark);
}

.review-question {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 12px;
  line-height: 1.4;
}

.question-number {
  color: var(--accent-primary);
  margin-right: 8px;
}

.review-answers {
  margin-bottom: 12px;
}

.review-answer {
  padding: 8px 12px;
  border-radius: 8px;
  margin-bottom: 8px;
  font-size: 14px;
}

.review-answer.correct {
  background: rgba(39, 174, 96, 0.1);
  color: var(--success);
  border-left: 4px solid var(--success);
}

.review-answer.incorrect {
  background: rgba(231, 76, 60, 0.1);
  color: var(--error);
  border-left: 4px solid var(--error);
}

.review-explanation {
  font-size: 14px;
  color: var(--text-secondary);
  line-height: 1.5;
  padding: 12px;
  background: var(--bg-primary);
  border-radius: 8px;
  box-shadow: var(--shadow-inset-light), var(--shadow-inset-dark);
}

/* History Content */
.history-stats {
  display: flex;
  justify-content: space-around;
  padding: 20px 24px;
  border-bottom: 1px solid rgba(127, 140, 141, 0.1);
}

.history-stat {
  text-align: center;
}

.history-stat .stat-label {
  display: block;
  font-size: 12px;
  color: var(--text-secondary);
  margin-bottom: 4px;
}

.history-stat .stat-value {
  display: block;
  font-size: 20px;
  font-weight: 700;
  color: var(--text-primary);
}

.history-content {
  padding: 24px;
  overflow-y: auto;
  flex: 1;
}

.no-history {
  text-align: center;
  color: var(--text-secondary);
  font-style: italic;
  padding: 40px;
}

.history-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  margin-bottom: 12px;
  background: var(--bg-secondary);
  border-radius: 12px;
  box-shadow: var(--shadow-inset-light), var(--shadow-inset-dark);
}

.history-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.history-category {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 16px;
}

.history-date {
  font-size: 12px;
  color: var(--text-secondary);
}

.history-score {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
}

.score-text {
  font-weight: 600;
  color: var(--text-primary);
}

.percentage-text {
  font-size: 14px;
  font-weight: 700;
  color: var(--accent-primary);
}

/* Responsive Design */
@media (max-width: 768px) {
  .dashboard-container {
    padding: 16px;
    gap: 20px;
  }
  
  .results-header {
    flex-direction: column;
    gap: 20px;
    padding: 24px 20px;
  }
  
  .results-title h1 {
    font-size: 28px;
  }
  
  .score-circle {
    width: 100px;
    height: 100px;
  }
  
  .score-percentage {
    font-size: 24px;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .stat-card {
    padding: 16px;
  }
  
  .stat-icon {
    width: 50px;
    height: 50px;
    font-size: 24px;
  }
  
  .stat-value {
    font-size: 20px;
  }
  
  .charts-container {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .chart-card {
    padding: 20px 16px;
  }
  
  .action-buttons {
    flex-direction: column;
    align-items: center;
  }
  
  .action-buttons .btn {
    width: 100%;
    max-width: 300px;
  }
  
  .modal-content {
    margin: 10px;
    max-height: 90vh;
  }
  
  .modal-header {
    padding: 20px;
  }
  
  .review-content,
  .history-content {
    padding: 20px;
  }
  
  .history-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .history-score {
    align-items: flex-start;
    flex-direction: row;
    gap: 12px;
  }
}
